{"name": "8it-webseite", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "node ./scripts/load-env.js .env.local 'vite'", "build": "node ./scripts/load-env.js .env.production 'tsc && vite build'", "preview": "vite preview", "start": "node ./scripts/load-env.js .env.production 'serve -s dist -l 5173'", "lint": "biome lint ./src ./convex ./scripts", "format": "biome format --write ./src ./convex ./scripts", "check": "biome check --write ./src ./convex ./scripts", "check-auth": "node ./scripts/check-auth.js", "check-production": "node ./scripts/production-check.js"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@tailwindcss/typography": "^0.5.16", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "serve": "^14.2.4", "tailwindcss": "^3.4.0", "typescript": "~5.7.2", "ultracite": "5.0.47", "vite": "^6.2.1"}, "dependencies": {"@types/node": "^22.13.10", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "convex": "^1.23.0", "framer-motion": "^12.5.0", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.3.0", "uuid": "^11.1.0"}}