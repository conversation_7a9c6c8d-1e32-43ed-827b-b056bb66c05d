import { fileURLToPath, URL } from 'node:url';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
  // Bestimme welche .env Datei geladen werden soll
  const envFile =
    process.env.NODE_ENV === 'production' ? '.env.production' : '.env.local';
  const _env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [react()],
    define: {
      // Explizit die Environment Variables aus der gewählten Datei laden
      __ENV_FILE__: JSON.stringify(envFile),
    },
    envPrefix: ['VITE_'], // Only load env vars that start with VITE_
    build: {
      outDir: 'dist',
      emptyOutDir: true,
      sourcemap: false,
      minify: 'esbuild',
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            router: ['react-router-dom'],
            motion: ['framer-motion'],
            convex: ['convex'],
            utils: ['uuid', 'react-intersection-observer'],
          },
        },
      },
      target: 'esnext',
      reportCompressedSize: false,
      chunkSizeWarningLimit: 1000,
    },
    server: {
      host: '0.0.0.0',
      port: 5173,
      allowedHosts: [
        'localhost',
        'www.innov8-it.de',
        'innov8-it.de',
        '25dd1d98-2bf5-4628-a6e7-3dbbde137e9f-00-94agp6mz6c7q.picard.replit.dev',
      ],
    },
    preview: {
      port: 5173,
      host: '0.0.0.0',
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
  };
});
