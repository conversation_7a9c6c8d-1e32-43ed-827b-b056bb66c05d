import { motion } from 'framer-motion';
import type React from 'react';
import { useEffect } from 'react';

export type ToastType = 'success' | 'error' | 'info' | 'warning';

interface ToastProps {
  message: string;
  type?: ToastType;
  duration?: number;
  onClose: () => void;
  isVisible: boolean;
}

const toastConfig = {
  success: {
    icon: 'fas fa-check-circle',
    bg: 'bg-green-500/95',
  },
  error: {
    icon: 'fas fa-exclamation-circle',
    bg: 'bg-red-500/95',
  },
  warning: {
    icon: 'fas fa-exclamation-triangle',
    bg: 'bg-yellow-500/95',
  },
  info: {
    icon: 'fas fa-info-circle',
    bg: 'bg-blue-500/95',
  },
};

const Toast: React.FC<ToastProps> = ({
  message,
  type = 'info',
  duration = 5000,
  onClose,
  isVisible,
}) => {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  if (!isVisible) {
    return null;
  }

  const { icon, bg } = toastConfig[type];

  return (
    <motion.div
      animate={{ x: 0, opacity: 1 }}
      className={`flex w-full max-w-sm items-start justify-between rounded-lg p-4 text-white shadow-2xl sm:max-w-md ${bg}`}
      exit={{ x: '100%', opacity: 0 }}
      initial={{ x: '100%', opacity: 0 }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
    >
      <div className="flex items-start">
        <i className={`${icon} mt-1 mr-4 text-xl`} />
        <p className="text-sm leading-relaxed">{message}</p>
      </div>
      <button
        className="mt-0.5 ml-2 cursor-pointer border-none bg-transparent p-0 text-lg text-white/80 transition-colors hover:text-white"
        onClick={onClose}
        type="button"
      >
        <i className="fas fa-times" />
      </button>
    </motion.div>
  );
};

export default Toast;
