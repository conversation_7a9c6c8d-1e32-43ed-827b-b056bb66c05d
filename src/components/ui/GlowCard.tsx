import type React from 'react';
import type { ReactNode } from 'react';

interface GlowCardProps {
  children: ReactNode;
  className?: string;
  iconClass?: string;
  title?: string;
  centerIcon?: boolean;
}

const GlowCard: React.FC<GlowCardProps> = ({
  children,
  className = '',
  iconClass,
  title,
  centerIcon = false,
}) => {
  return (
    <div
      className={`group hover:-translate-y-1.5 flex h-full flex-col overflow-hidden rounded-xl border border-border-color bg-card-background text-left shadow-lg transition-all duration-300 hover:border-primary hover:shadow-2xl hover:shadow-primary/20 ${className}`}
    >
      {iconClass && (
        <div
          className={`
            ${
              centerIcon
                ? 'flex h-36 items-center justify-center border-border-color border-b bg-white/5 p-8 text-5xl transition-all duration-300 group-hover:scale-105 group-hover:bg-primary/10 md:h-40 md:p-10 md:text-6xl'
                : 'mb-6 px-7 text-5xl text-primary'
            } `}
        >
          <i className={iconClass} />
        </div>
      )}
      {title && (
        <h3 className="mt-7 mb-3 px-7 font-semibold text-text-color text-xl md:text-2xl">
          {title}
        </h3>
      )}
      <div className="flex flex-grow flex-col px-7 pb-7">{children}</div>
    </div>
  );
};

export default GlowCard;
