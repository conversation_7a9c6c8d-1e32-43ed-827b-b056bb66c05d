import { AnimatePresence } from 'framer-motion';
import type React from 'react';
import Toast, { type ToastType } from './Toast';

export interface ToastData {
  id: string;
  message: string;
  type: ToastType;
}

interface ToastContainerProps {
  toasts: ToastData[];
  onRemoveToast: (id: string) => void;
}

const ToastContainer: React.FC<ToastContainerProps> = ({
  toasts,
  onRemoveToast,
}) => {
  return (
    <div className="fixed right-5 bottom-5 z-50 flex flex-col gap-3 sm:right-10 sm:bottom-10">
      <AnimatePresence>
        {toasts.map((toast) => (
          <Toast
            isVisible={true}
            key={toast.id}
            message={toast.message}
            onClose={() => onRemoveToast(toast.id)}
            type={toast.type}
          />
        ))}
      </AnimatePresence>
    </div>
  );
};

export default ToastContainer;
