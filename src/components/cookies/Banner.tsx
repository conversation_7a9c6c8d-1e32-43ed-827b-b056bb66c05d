import { AnimatePresence, motion } from 'framer-motion';
import type React from 'react';
import { useState } from 'react';
import { useCookieConsent } from '@/components/context/CookieContext';

const SettingsToggle: React.FC<{
  name: string;
  checked: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
}> = ({ name, checked, onChange, disabled }) => (
  <label className="relative inline-block h-6 w-11" htmlFor={name}>
    <input
      checked={checked}
      className="peer h-0 w-0 opacity-0"
      disabled={disabled}
      id={name}
      name={name}
      onChange={onChange}
      type="checkbox"
    />
    <span className="absolute top-0 right-0 bottom-0 left-0 cursor-pointer rounded-full bg-gray-600 transition-colors peer-checked:bg-primary" />
    <span className="absolute bottom-1 left-1 h-4 w-4 rounded-full bg-white transition-transform content-[''] peer-checked:translate-x-5" />
  </label>
);

const CookieBanner: React.FC = () => {
  const {
    showBanner,
    setShowBanner,
    acceptAll,
    declineAll,
    savePreferences,
    consent,
  } = useCookieConsent();
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState({
    external: consent.external,
  });

  const handlePreferenceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setPreferences((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSavePreferences = () => {
    savePreferences(preferences);
    setShowBanner(false);
  };

  return (
    <AnimatePresence>
      {showBanner && (
        <motion.div
          animate={{ y: 0, opacity: 1 }}
          className="fixed bottom-0 left-0 z-50 w-full border-border-color border-t bg-card-background/95 shadow-t-lg backdrop-blur-sm"
          exit={{ y: '100%', opacity: 0 }}
          initial={{ y: '100%', opacity: 0 }}
          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        >
          <div className="container mx-auto max-w-4xl p-4">
            {showDetails ? (
              // Details View
              <div className="text-white">
                <div className="mb-4 flex items-center gap-3">
                  <i className="fas fa-cookie-bite text-2xl text-primary" />
                  <h2 className="font-bold text-xl">Cookie-Einstellungen</h2>
                </div>
                <div className="max-h-[40vh] space-y-3 overflow-y-auto pr-2">
                  {/* Necessary Cookies */}
                  <div className="flex items-center justify-between rounded-lg border border-border-color bg-background/50 p-3">
                    <div>
                      <h3 className="font-semibold">Notwendige Cookies</h3>
                      <p className="text-text-color/80 text-xs">
                        Diese Cookies sind für das Funktionieren der Website
                        erforderlich.
                      </p>
                    </div>
                    <SettingsToggle checked={true} disabled name="necessary" />
                  </div>
                  {/* External Content */}
                  <div className="flex items-center justify-between rounded-lg border border-border-color bg-background/50 p-3">
                    <div>
                      <h3 className="font-semibold">Externe Inhalte</h3>
                      <p className="text-text-color/80 text-xs">
                        Aktiviert externe Inhalte wie Google Maps und
                        Partner-Links.
                      </p>
                    </div>
                    <SettingsToggle
                      checked={preferences.external}
                      name="external"
                      onChange={handlePreferenceChange}
                    />
                  </div>
                </div>
                <div className="mt-4 flex flex-col gap-3 sm:flex-row">
                  <button
                    className="flex-1 rounded-md border border-border-color bg-transparent px-4 py-2 text-sm text-text-color/90 transition-colors hover:bg-border-color sm:flex-auto"
                    onClick={() => setShowDetails(false)}
                    type="button"
                  >
                    Zurück
                  </button>
                  <button
                    className="flex-1 rounded-md bg-primary px-4 py-2 font-semibold text-sm text-white transition-colors hover:bg-secondary sm:flex-auto"
                    onClick={handleSavePreferences}
                    type="button"
                  >
                    Einstellungen speichern
                  </button>
                </div>
              </div>
            ) : (
              // Initial View
              <div>
                <div className="mb-2 flex items-center gap-3">
                  <i className="fas fa-cookie-bite text-primary text-xl" />
                  <h2 className="font-bold text-lg text-white">
                    Wir verwenden Cookies
                  </h2>
                </div>
                <p className="mb-4 text-sm text-text-color/80">
                  Diese Website nutzt Cookies für eine bessere Nutzererfahrung
                  und für essenzielle Funktionen.
                </p>
                <div className="flex flex-col gap-2 sm:flex-row sm:gap-3">
                  <button
                    className="flex-1 rounded-md border border-border-color bg-background px-4 py-2 text-text-color/80 text-xs transition-colors hover:bg-border-color sm:text-sm"
                    onClick={() => {
                      declineAll();
                      setShowBanner(false);
                    }}
                    type="button"
                  >
                    Nur notwendige
                  </button>
                  <button
                    className="flex-1 rounded-md border border-primary bg-transparent px-4 py-2 text-primary text-xs transition-colors hover:bg-primary/10 sm:text-sm"
                    onClick={() => setShowDetails(true)}
                    type="button"
                  >
                    Anpassen
                  </button>
                  <button
                    className="flex-1 rounded-md bg-primary px-4 py-2 font-semibold text-white text-xs transition-colors hover:bg-secondary sm:text-sm"
                    onClick={() => {
                      acceptAll();
                      setShowBanner(false);
                    }}
                    type="button"
                  >
                    Alle akzeptieren
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CookieBanner;
