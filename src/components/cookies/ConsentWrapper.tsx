import type React from 'react';
import type { ReactNode } from 'react';
import {
  type ConsentType,
  useCookieConsent,
} from '@/components/context/CookieContext';

interface ConsentWrapperProps {
  type: ConsentType;
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Wraps content that requires specific cookie consent
 * Only renders children if the required consent is given
 * Otherwise renders the fallback component
 */
const ConsentWrapper: React.FC<ConsentWrapperProps> = ({
  type,
  children,
  fallback,
}) => {
  const { isConsentGiven, setShowBanner } = useCookieConsent();

  if (isConsentGiven(type)) {
    return <>{children}</>;
  }

  return fallback ? (
    fallback
  ) : (
    <div className="content-permission">
      <div className="content-permission-inner">
        <i className="fas fa-cookie-bite" />
        <h3>Zustimmung erforderlich</h3>
        <p>
          Dieser Inhalt erfordert Ihre Zustimmung zur Verwendung von Cookies.
        </p>
        <button
          className="permission-settings-button"
          onClick={() => setShowBanner(true)}
          type="button"
        >
          Einstellungen ändern
        </button>
      </div>
      <style>
        {`
        .content-permission {
          border: 1px solid var(--border-color);
          border-radius: 8px;
          padding: 2rem;
          background-color: var(--background-color);
          text-align: center;
          width: 100%;
          height: 100%;
          min-height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .content-permission-inner {
          max-width: 400px;
        }
        
        .content-permission i {
          font-size: 2.5rem;
          color: var(--primary-color);
          margin-bottom: 1rem;
        }
        
        .content-permission h3 {
          font-size: 1.5rem;
          margin-bottom: 1rem;
          color: var(--text-color);
        }
        
        .content-permission p {
          margin-bottom: 1.5rem;
          color: var(--text-color);
          opacity: 0.8;
        }
        
        .permission-settings-button {
          background-color: var(--primary-color);
          color: white;
          border: none;
          border-radius: 4px;
          padding: 0.7rem 1.2rem;
          font-size: 1rem;
          cursor: pointer;
          transition: background-color 0.3s ease;
        }
        
        .permission-settings-button:hover {
          background-color: var(--secondary-color);
        }
        `}
      </style>
    </div>
  );
};

export default ConsentWrapper;
