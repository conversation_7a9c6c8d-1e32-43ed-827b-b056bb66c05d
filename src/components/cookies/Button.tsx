import { motion } from 'framer-motion';
import type React from 'react';
import { useState } from 'react';
import { useCookieConsent } from '@/components/context/CookieContext';

const CookieButton: React.FC = () => {
  const [isHovered, setIsHovered] = useState(false);
  const { setShowBanner } = useCookieConsent();

  const handleButtonClick = () => {
    setShowBanner(true);
  };

  return (
    <motion.button
      className="fixed right-5 bottom-5 z-40 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-2xl text-white shadow-lg transition-colors hover:bg-secondary sm:right-6 sm:bottom-6 sm:h-14 sm:w-14"
      id="preferences-settings-button"
      initial={{ scale: 1 }}
      onClick={handleButtonClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <i className="fas fa-cookie-bite" />
      {isHovered && (
        <motion.span
          animate={{ opacity: 1, x: 0 }}
          className="absolute right-full mr-3 whitespace-nowrap rounded-md bg-card-background px-3 py-2 font-medium text-sm text-text-color shadow-md"
          exit={{ opacity: 0, x: 10 }}
          initial={{ opacity: 0, x: 10 }}
        >
          Cookie-Einstellungen
        </motion.span>
      )}
    </motion.button>
  );
};

export default CookieButton;
