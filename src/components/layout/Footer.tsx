import { Link } from 'react-router-dom';
import logo from '@/assets/logo.svg';
import { useCookieConsent } from '@/components/context/CookieContext';
import { ConsentWrapper } from '@/components/cookies';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { setShowBanner } = useCookieConsent();

  return (
    <footer className="relative mt-8 bg-footer-background/90 pt-12 text-text-color backdrop-blur-md">
      <div className="mx-auto max-w-6xl px-4">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {/* Logo and Copyright */}
          <div className="text-center md:col-span-1 md:text-left">
            <Link to="/">
              <div className="mb-4 inline-block">
                <img alt="innov8-IT Logo" className="h-12" src={logo} />
              </div>
            </Link>
            <p className="mb-4">Innovative IT-Lösungen für Ihr Unternehmen</p>
            <p className="mt-6 text-sm opacity-80">
              &copy; {currentYear} innov8-IT. Alle Rechte vorbehalten.
            </p>
          </div>

          {/* Links Sections */}
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:col-span-2">
            <div className="footer-section">
              <h3 className="after:-bottom-2 relative mb-5 font-semibold text-lg text-white after:absolute after:left-0 after:h-0.5 after:w-10 after:bg-primary after:content-['']">
                Kontakt
              </h3>
              <ul className="space-y-3">
                <li>
                  <a
                    className="flex items-center transition-colors hover:text-primary"
                    href="mailto:<EMAIL>"
                  >
                    <i className="fas fa-envelope mr-3 w-4 text-center text-primary" />
                    <EMAIL>
                  </a>
                </li>
                <li>
                  <a
                    className="flex items-center transition-colors hover:text-primary"
                    href="tel:+49 2161 9850066"
                  >
                    <i className="fas fa-phone mr-3 w-4 text-center text-primary" />
                    +49 2161 9850066
                  </a>
                </li>
                <li>
                  <ConsentWrapper
                    fallback={
                      <button
                        className="group relative flex items-center text-left text-text-color/80 transition-colors hover:text-primary"
                        onClick={() => {
                          setShowBanner(true);
                        }}
                        type="button"
                      >
                        <i className="fas fa-map-marker-alt mr-3 w-4 text-center text-primary" />
                        <div>
                          Schulstraße 60, 41065 Mönchengladbach
                          <span className="absolute top-full left-0 z-10 mt-2 hidden items-center gap-2 whitespace-nowrap rounded-md border border-border-color bg-card-background px-2 py-1 text-xs shadow-lg group-hover:flex">
                            <i className="fas fa-cookie-bite text-primary" />
                            Externe Inhalte aktivieren
                          </span>
                        </div>
                      </button>
                    }
                    type="external"
                  >
                    <a
                      className="flex items-center transition-colors hover:text-primary"
                      href="https://maps.google.com/?q=Schulstraße+60,+41065+Mönchengladbach"
                      rel="noopener noreferrer"
                      target="_blank"
                    >
                      <i className="fas fa-map-marker-alt mr-3 w-4 text-center text-primary" />
                      Schulstraße 60, 41065 Mönchengladbach
                    </a>
                  </ConsentWrapper>
                </li>
                <li>
                  <span className="flex items-center">
                    <i className="fas fa-clock mr-3 w-4 text-center text-primary" />
                    Mo. - Fr.: 08:00 - 18:00 Uhr
                  </span>
                </li>
              </ul>
            </div>

            <div className="footer-section">
              <h3 className="after:-bottom-2 relative mb-5 font-semibold text-lg text-white after:absolute after:left-0 after:h-0.5 after:w-10 after:bg-primary after:content-['']">
                Rechtliches
              </h3>
              <ul className="space-y-3">
                <li>
                  <Link
                    className="transition-colors hover:text-primary"
                    to="/impressum"
                  >
                    Impressum
                  </Link>
                </li>
                <li>
                  <Link
                    className="transition-colors hover:text-primary"
                    to="/datenschutz"
                  >
                    Datenschutz
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
