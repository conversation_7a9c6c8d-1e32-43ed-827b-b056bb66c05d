import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import logo from '@/assets/logo.svg';

const Navbar = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { path: '/', label: 'Home' },
    { path: '/leistungen', label: 'Leistungen' },
    { path: '/kontakt', label: 'Kontakt' },
  ];

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="fixed top-0 left-0 z-50 w-full border-border-color border-b bg-header-background/90 backdrop-blur-md">
      <div className="mx-auto max-w-6xl px-4">
        <nav className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link className="flex-shrink-0" to="/">
            <img alt="innov8-IT Logo" className="h-8 w-auto" src={logo} />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden items-center space-x-8 md:flex">
            {navItems.map((item) => (
              <Link
                className={`relative px-3 py-2 font-medium text-sm transition-colors duration-200 ${
                  location.pathname === item.path
                    ? 'text-primary'
                    : 'text-text-color hover:text-primary'
                }`}
                key={item.path}
                to={item.path}
              >
                {item.label}
                {location.pathname === item.path && (
                  <span className="absolute bottom-0 left-0 h-0.5 w-full rounded-full bg-primary" />
                )}
              </Link>
            ))}
          </div>

          {/* Desktop Support Button */}
          <div className="hidden md:block">
            <a
              className="inline-flex items-center rounded-lg bg-primary px-4 py-2 font-medium text-sm text-white transition-colors duration-200 hover:bg-primary/90"
              href="https://download.anydesk.com/AnyDesk.exe"
              rel="noopener noreferrer"
              target="_blank"
            >
              <i className="fas fa-headset mr-2" />
              Remotesupport
            </a>
          </div>

          {/* Mobile menu button */}
          <button
            className="flex h-10 w-10 items-center justify-center text-text-color transition-colors duration-200 hover:text-primary md:hidden"
            onClick={toggleMobileMenu}
            type="button"
          >
            <i
              className={`fas ${isMobileMenuOpen ? 'fa-times' : 'fa-bars'} text-lg`}
            />
          </button>
        </nav>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="border-border-color border-t bg-card-background md:hidden">
            <div className="space-y-1 px-2 pt-2 pb-3">
              {navItems.map((item) => (
                <Link
                  className={`block rounded-md px-3 py-2 font-medium text-base transition-colors duration-200 ${
                    location.pathname === item.path
                      ? 'bg-primary/10 text-primary'
                      : 'text-text-color hover:bg-primary/5 hover:text-primary'
                  }`}
                  key={item.path}
                  onClick={() => setIsMobileMenuOpen(false)}
                  to={item.path}
                >
                  {item.label}
                </Link>
              ))}

              {/* Mobile Support Button */}
              <a
                className="mt-4 block rounded-md bg-primary px-3 py-2 text-center text-white transition-colors duration-200 hover:bg-primary/90"
                href="https://download.anydesk.com/AnyDesk.exe"
                onClick={() => setIsMobileMenuOpen(false)}
                rel="noopener noreferrer"
                target="_blank"
              >
                <i className="fas fa-headset mr-2" />
                Remotesupport
              </a>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Navbar;
