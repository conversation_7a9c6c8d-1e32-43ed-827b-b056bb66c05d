import { useEffect, useRef, useState } from 'react';
import { useCookieConsent } from '@/components/context/CookieContext';
import { ConsentWrapper } from '@/components/cookies';

interface Partner {
  filename: string;
  displayName: string;
  description: string;
  website: string;
  category: 'Hardware' | 'Software' | 'Sonstiges';
}

const PartnerLogos = () => {
  const { isConsentGiven } = useCookieConsent();
  const [partners, setPartners] = useState<Partner[]>([]);
  const [activeDescription, setActiveDescription] = useState<string | null>(
    null
  );
  const descriptionTimerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setPartners([
      {
        filename: 'synology.png',
        displayName: 'Synology',
        description:
          'Führender Anbieter von NAS-Lösungen für Datenspeicherung und -sicherung mit innovativen Cloud-Services.',
        website: 'https://www.synology.com/de-de',
        category: 'Hardware',
      },
      {
        filename: 'terra.png',
        displayName: 'Terra',
        description:
          'Deutscher Hersteller hochwertiger Computer-Hardware und IT-Lösungen für Unternehmen.',
        website: 'https://www.terra.de',
        category: 'Hardware',
      },
      {
        filename: 'elovade.png',
        displayName: 'Elovade',
        description:
          'Spezialist für IT-Sicherheit und Managed Services mit Fokus auf Unternehmensnetzwerke.',
        website: 'https://www.elovade.com',
        category: 'Sonstiges',
      },
      {
        filename: 'mailstore.svg',
        displayName: 'Mailstore',
        description:
          'Professionelle E-Mail-Archivierungslösung für rechtssichere Datenspeicherung.',
        website: 'https://www.mailstore.com/de',
        category: 'Software',
      },
      {
        filename: 'servereye.png',
        displayName: 'ServerEye',
        description:
          'Umfassendes Monitoring-System für IT-Infrastrukturen mit proaktiver Fehlererkennung.',
        website: 'https://www.server-eye.de',
        category: 'Software',
      },
      {
        filename: 'backupassist.png',
        displayName: 'BackupAssist',
        description:
          'Umfassende Softwarelösungen für Backup und Disaster Recovery, um Datenverluste zu vermeiden.',
        website: 'https://www.backupassist.com/',
        category: 'Software',
      },
      {
        filename: 'eset.png',
        displayName: 'ESET',
        description:
          'Führender Anbieter von Sicherheitslösungen für Privatanwender und Unternehmen, bekannt für robuste Antiviren-Software.',
        website: 'https://www.eset.com/',
        category: 'Software',
      },
      {
        filename: 'hornetsecurity.png',
        displayName: 'Hornetsecurity',
        description:
          'Führender Anbieter von Security- und Backup-Lösungen für E-Mail-Daten mit umfassendem Schutz und Zuverlässigkeit.',
        website: 'https://www.hornetsecurity.com/',
        category: 'Software',
      },
      {
        filename: 'n-able.png',
        displayName: 'N-able',
        description:
          'Führendes Unternehmen für Netzwerksicherheits- und Managementlösungen mit speziellem Fokus auf KMU-Unterstützung.',
        website: 'https://www.n-able.com/',
        category: 'Software',
      },
    ]);
  }, []);

  const handleDescriptionToggle = (
    partnerId: string | null,
    enter: boolean
  ) => {
    if (descriptionTimerRef.current) {
      clearTimeout(descriptionTimerRef.current);
      descriptionTimerRef.current = null;
    }

    if (enter) {
      setActiveDescription(partnerId);
    } else {
      // Add small delay before hiding to prevent flickering
      descriptionTimerRef.current = setTimeout(() => {
        setActiveDescription(null);
      }, 100);
    }
  };

  const handlePartnerClick = (website: string) => {
    if (isConsentGiven('external')) {
      window.open(website, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <section
      aria-labelledby="partnerschaften-heading"
      className="partner-logos-container"
      id="partnerschaften"
    >
      <h2 id="partnerschaften-heading">Unsere Partnerschaften</h2>
      <p className="partner-intro" />

      <div className="partner-grid">
        {partners.map((partner, _index) => (
          <ConsentWrapper
            fallback={
              <div className="partner-card inactive">
                <button
                  className="info-button"
                  onMouseEnter={() =>
                    handleDescriptionToggle(partner.displayName, true)
                  }
                  onMouseLeave={() =>
                    handleDescriptionToggle(partner.displayName, false)
                  }
                  type="button"
                >
                  <i className="fas fa-info" />
                </button>
                <div className="partner-logo-container">
                  <img
                    alt={`${partner.displayName} Logo`}
                    src={`/partnerschaften/${partner.filename}`}
                  />
                </div>
                <div
                  className={`description-panel ${activeDescription === partner.displayName ? 'active' : ''}`}
                >
                  <div className="description-content">
                    {partner.description}
                  </div>
                </div>
                <div className="partner-name-overlay">
                  <h3 className="partner-name">{partner.displayName}</h3>
                </div>
                <div className="cookie-hint">
                  <i className="fas fa-cookie-bite" />
                  <span>Externe Inhalte aktivieren</span>
                </div>
              </div>
            }
            key={partner.displayName}
            type="external"
          >
            <button
              className="partner-card active"
              onClick={() => handlePartnerClick(partner.website)}
              type="button"
            >
              <button
                className="info-button"
                onClick={(e) => e.stopPropagation()}
                onMouseEnter={() =>
                  handleDescriptionToggle(partner.displayName, true)
                }
                onMouseLeave={() =>
                  handleDescriptionToggle(partner.displayName, false)
                }
                type="button"
              >
                <i className="fas fa-info" />
              </button>
              <div className="partner-logo-container">
                <img
                  alt={`${partner.displayName} Logo`}
                  src={`/partnerschaften/${partner.filename}`}
                />
              </div>
              <div
                className={`description-panel ${activeDescription === partner.displayName ? 'active' : ''}`}
              >
                <div className="description-content">{partner.description}</div>
              </div>
              <div className="partner-name-overlay">
                <h3 className="partner-name">{partner.displayName}</h3>
              </div>
            </button>
          </ConsentWrapper>
        ))}
      </div>

      <style>
        {`
        .partner-logos-container {
          padding: 3rem 2rem;
          background-color: transparent;
          border-radius: 16px;
          box-shadow: none;
          text-align: center;
          margin: 2rem auto;
          max-width: 1200px;
          border: none;
        }

        .partner-logos-container h2 {
          font-size: 2.5rem;
          margin-bottom: 1.5rem;
          color: var(--text-color);
        }

        .partner-intro {
          max-width: 800px;
          margin: 1.5rem auto 2.5rem auto;
          font-size: 1.15rem;
          color: var(--text-color);
          opacity: 0.85;
          line-height: 1.7;
        }

        .partner-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
          gap: 1.5rem;
          width: 100%;
        }

        .partner-card {
          position: relative;
          background-color: rgba(30, 30, 30, 0.4);
          border-radius: 12px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          overflow: hidden;
          transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
          height: 160px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          display: flex;
          flex-direction: column;
          backdrop-filter: blur(15px);
        }
        
        .partner-card.active {
          cursor: pointer;
        }

        .partner-card.active:hover {
          transform: translateY(-6px);
          box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12), 0 0 15px rgba(52, 152, 219, 0.3);
          border-color: var(--primary-color);
        }
        
        .info-button {
          position: absolute;
          top: 10px;
          right: 10px;
          z-index: 20;
          cursor: pointer;
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.6);
          transition: all 0.3s ease;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .info-button i {
          font-size: 0.9rem;
          color: var(--primary-color);
          transition: all 0.3s ease;
        }

        .info-button:hover {
          background-color: #333;
          transform: scale(1.1);
        }

        .partner-logo-container {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(15, 15, 15, 0.7);
          padding: 1rem;
          position: relative;
          transition: background-color 0.3s ease;
        }

        .partner-logo-container::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, 
            rgba(0, 0, 0, 0.1) 0%, 
            rgba(0, 0, 0, 0) 100%);
          z-index: 1;
        }

        .partner-logo-container img {
          max-width: 85%;
          max-height: 75%;
          object-fit: contain;
          transition: all 0.4s ease;
          filter: grayscale(100%) brightness(0.8) contrast(1.2);
          z-index: 2;
        }

        .partner-card:hover .partner-logo-container img {
          filter: grayscale(0%) brightness(1) contrast(1);
          transform: scale(1.1);
        }
        
        .partner-card:hover .partner-logo-container {
          background-color: rgba(52, 152, 219, 0.08);
        }
        
        .description-panel {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(20, 20, 20, 0.95);
          opacity: 0;
          visibility: hidden;
          transform: translateY(10px);
          transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 1.5rem;
          border-radius: 12px;
          z-index: 15;
          overflow: hidden;
        }
        
        .description-panel::before {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 50px;
          height: 50px;
          background: radial-gradient(circle at top right, 
            rgba(52, 152, 219, 0.15) 0%, 
            rgba(52, 152, 219, 0) 70%);
          z-index: -1;
        }
        
        .description-panel.active {
          opacity: 1;
          visibility: visible;
          transform: translateY(0);
          border: 1px solid rgba(52, 152, 219, 0.2);
        }
        
        .description-content {
          color: white;
          text-align: left;
          font-size: 0.9rem;
          line-height: 1.5;
          animation: fadeIn 0.3s ease forwards;
          animation-delay: 0.1s;
          opacity: 0;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        .partner-name-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(to top, 
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.8) 50%,
            rgba(0, 0, 0, 0) 100%);
          padding: 2rem 1rem 0.8rem 1rem;
          transform: translateY(100%);
          transition: transform 0.3s ease;
          z-index: 5;
        }

        .partner-card:hover .partner-name-overlay {
          transform: translateY(0);
        }

        .partner-name {
          font-size: 1rem;
          color: white;
          margin: 0;
          font-weight: 600;
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9);
        }

        .cookie-hint {
          position: absolute;
          bottom: 5px;
          left: 0;
          right: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          font-size: 0.75rem;
          color: var(--text-color);
          opacity: 0.7;
          z-index: 5;
        }

        .cookie-hint i {
          color: #aaa;
        }

        .inactive .partner-logo-container img {
          filter: grayscale(100%) brightness(0.5) contrast(1);
        }

        @media (max-width: 768px) {
          .partner-logos-container {
            padding: 2rem 1rem;
            margin: 1rem auto;
          }

          .partner-logos-container h2 {
            font-size: 1.8rem;
          }

          .partner-intro {
            font-size: 1rem;
            margin-bottom: 1.5rem;
          }

          .partner-grid {
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 1rem;
          }

          .partner-card {
            height: 140px;
          }
          
          .description-content {
            font-size: 0.8rem;
            line-height: 1.4;
          }
          
          .description-panel {
            padding: 1.2rem;
          }
        }

        @media (max-width: 480px) {
          .partner-logos-container {
            padding: 1.5rem 0.8rem;
          }
          
          .partner-grid {
            grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
            gap: 0.8rem;
          }
          
          .partner-card {
            height: 120px;
          }
          
          .partner-name {
            font-size: 0.9rem;
          }

          .cookie-hint {
            font-size: 0.75rem;
          }

          .info-button {
            width: 24px;
            height: 24px;
          }

          .info-button i {
            font-size: 0.8rem;
          }
          
          .description-content {
            font-size: 0.75rem;
            line-height: 1.3;
          }
          
          .description-panel {
            padding: 1rem;
          }
        }
        `}
      </style>
    </section>
  );
};

export default PartnerLogos;
