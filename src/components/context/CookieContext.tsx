import type React from 'react';
import {
  createContext,
  type ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react';

export type ConsentType = 'necessary' | 'external';

interface CookieConsent {
  necessary: boolean; // immer true
  external: boolean; // Zustimmung für z. B. Google Maps
}

interface CookieContextType {
  consent: CookieConsent;
  showBanner: boolean;
  setShowBanner(show: boolean): void;
  acceptAll(): void;
  declineAll(): void;
  savePreferences(prefs: Partial<CookieConsent>): void;
  isConsentGiven(type: ConsentType): boolean;
}

const CookieContext = createContext<CookieContextType | null>(null);

const COOKIE_EXPIRATION_DAYS = 3;
const STORAGE_KEY = 'sitePreferences';

const defaultConsent: CookieConsent = {
  necessary: true,
  external: false,
};

const saveConsentToCookies = (consent: CookieConsent) => {
  const expiry = new Date();
  expiry.setDate(expiry.getDate() + COOKIE_EXPIRATION_DAYS);

  const consentWithExpiry = {
    ...consent,
    expiry: expiry.toISOString(),
  };

  localStorage.setItem(STORAGE_KEY, JSON.stringify(consentWithExpiry));
};

const loadConsentFromCookies = (): CookieConsent | null => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return null;
    }

    const parsed = JSON.parse(stored);

    // Prüfe Ablaufzeit
    if (parsed.expiry && new Date(parsed.expiry) < new Date()) {
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }

    return {
      necessary: parsed.necessary ?? true,
      external: parsed.external ?? false,
    };
  } catch {
    return null;
  }
};

interface CookieProviderProps {
  children: ReactNode;
}

export const CookieProvider: React.FC<CookieProviderProps> = ({ children }) => {
  const [consent, setConsent] = useState<CookieConsent>(defaultConsent);
  const [showBanner, setShowBanner] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Nach hydration prüfen
    const savedConsent = loadConsentFromCookies();
    if (savedConsent) {
      setConsent(savedConsent);
      setShowBanner(false);
    } else {
      setShowBanner(true);
    }
    setIsHydrated(true);
  }, []);

  const acceptAll = () => {
    const newConsent: CookieConsent = {
      necessary: true,
      external: true,
    };
    setConsent(newConsent);
    saveConsentToCookies(newConsent);
    setShowBanner(false);
  };

  const declineAll = () => {
    const newConsent: CookieConsent = {
      necessary: true,
      external: false,
    };
    setConsent(newConsent);
    saveConsentToCookies(newConsent);
    setShowBanner(false);
  };

  const savePreferences = (prefs: Partial<CookieConsent>) => {
    const newConsent: CookieConsent = {
      necessary: true, // immer true
      external: prefs.external ?? consent.external,
    };
    setConsent(newConsent);
    saveConsentToCookies(newConsent);
    setShowBanner(false);
  };

  const isConsentGiven = (type: ConsentType): boolean => {
    return consent[type];
  };

  const value: CookieContextType = {
    consent,
    showBanner: showBanner && isHydrated,
    setShowBanner,
    acceptAll,
    declineAll,
    savePreferences,
    isConsentGiven,
  };

  return (
    <CookieContext.Provider value={value}>{children}</CookieContext.Provider>
  );
};

export const useCookieConsent = () => {
  const ctx = useContext(CookieContext);
  if (!ctx) {
    throw new Error('useCookieConsent must be used within a CookieProvider');
  }
  return ctx;
};
