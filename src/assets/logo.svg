<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="300" height="100" viewBox="0 0 300 100" version="1.1" xmlns="http://www.w3.org/2000/svg"
    xmlns:svg="http://www.w3.org/2000/svg">

    <!-- Hintergrund (optional, transparent) -->
    <rect width="300" height="100" fill="none" rx="10" ry="10" />

    <!-- Haupttext "innov8" mit modernem Gradient -->
    <defs>
        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#2962FF;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#0091EA;stop-opacity:1" />
        </linearGradient>
    </defs>

    <!-- Zentrierte Positionierung des Logos -->
    <text x="50" y="65" font-family="Arial, sans-serif" font-weight="bold" font-size="48"
        fill="url(#gradient1)">innov8</text>

    <!-- Bindestrich separat positioniert - etwas tiefer -->
    <text x="204" y="62" font-family="Arial, sans-serif" font-weight="bold" font-size="48" fill="#424242">-</text>

    <!-- IT Teil mit angepasstem Abstand -->
    <text x="222" y="65" font-family="Arial, sans-serif" font-weight="bold" font-size="48" fill="#424242">IT</text>

    <!-- Innovativer Effekt: Verbindungslinien, die Technologie symbolisieren -->
    <g stroke="#0091EA" stroke-width="1.5" opacity="0.7">
        <line x1="30" y1="75" x2="270" y2="75" />
        <line x1="40" y1="80" x2="260" y2="80" />
        <circle cx="155" cy="75" r="3" fill="#2962FF" />
        <circle cx="195" cy="80" r="2" fill="#2962FF" />
        <circle cx="115" cy="80" r="2" fill="#2962FF" />
    </g>

    <!-- Subtiler Slogan (optional) -->
    <text x="150" y="95" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#757575">Innovative
        IT-Lösungen</text>

</svg>