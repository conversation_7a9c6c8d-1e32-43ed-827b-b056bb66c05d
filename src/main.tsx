import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import App from '@/App';
import '@/styles/index.css';
import { ConvexProvider, ConvexReactClient } from 'convex/react';
import { <PERSON>ieProvider, ToastProvider } from '@/components/context';

const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string);

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found');
}

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <CookieProvider>
      <ConvexProvider client={convex}>
        <ToastProvider>
          <BrowserRouter>
            <App />
          </BrowserRouter>
        </ToastProvider>
      </ConvexProvider>
    </CookieProvider>
  </React.StrictMode>
);
