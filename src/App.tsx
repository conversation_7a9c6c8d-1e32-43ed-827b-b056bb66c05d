import { AnimatePresence } from 'framer-motion';
import { useEffect } from 'react';
import { Route, Routes, useLocation } from 'react-router-dom';

import { <PERSON><PERSON>Provider } from '@/components/context';
import { <PERSON>, Button } from '@/components/cookies';
import { Footer, Navbar, PageTransition } from '@/components/layout';
import { AnimatedBackground } from '@/components/ui';
import Datenschutz from '@/pages/datenschutz';
import Home from '@/pages/home';
import Impressum from '@/pages/impressum';
import Kontakt from '@/pages/kontakt';
import Leistungen from '@/pages/leistungen';

function App() {
  const location = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <CookieProvider>
      <AnimatedBackground />
      <div
        className="relative min-h-screen text-text-color"
        style={{ zIndex: 10, backgroundColor: 'transparent' }}
      >
        <Navbar />

        <main className="pt-16">
          <AnimatePresence mode="wait">
            <Routes key={location.pathname} location={location}>
              <Route
                element={
                  <PageTransition>
                    <Home />
                  </PageTransition>
                }
                path="/"
              />
              <Route
                element={
                  <PageTransition>
                    <Leistungen />
                  </PageTransition>
                }
                path="/leistungen"
              />
              <Route
                element={
                  <PageTransition>
                    <Kontakt />
                  </PageTransition>
                }
                path="/kontakt"
              />
              <Route
                element={
                  <PageTransition>
                    <Impressum />
                  </PageTransition>
                }
                path="/impressum"
              />
              <Route
                element={
                  <PageTransition>
                    <Datenschutz />
                  </PageTransition>
                }
                path="/datenschutz"
              />
            </Routes>
          </AnimatePresence>
        </main>

        <Footer />
        <Banner />
        <Button />
      </div>
    </CookieProvider>
  );
}

export default App;
