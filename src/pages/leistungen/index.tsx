import { motion } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { ScrollAnimation } from '@/components/common';
import { GlowCard } from '@/components/ui';

const Leistungen = () => {
  const services = [
    {
      id: 1,
      title: 'IT-Beratung',
      description:
        'Professionelle Beratung für die optimale IT-Strategie Ihres Unternehmens.',
      features: [
        'Analyse Ihrer IT-Infrastruktur',
        'Entwicklung einer maßgeschneiderten IT-Strategie',
        'Optimierung von Geschäftsprozessen',
        'Kostenanalyse und Budgetplanung',
      ],
      icon: 'fas fa-comments',
    },
    {
      id: 2,
      title: 'IT-Support',
      description:
        'Zuverlässiger technischer Support für alle Ihre IT-Anforderungen.',
      features: [
        'Helpdesk und Remote-Support',
        'Vor-Ort-Service bei Bedarf',
        'Schnelle Reaktionszeiten',
        'Proaktive Wartung und Updates',
      ],
      icon: 'fas fa-headset',
    },
    {
      id: 3,
      title: 'Cloud-Lösungen',
      description: 'Sichere und skalierbare Cloud-Dienste für Ihr Unternehmen.',
      features: [
        'Cloud-Migration und -Strategie',
        'Infrastructure as a Service (IaaS)',
        'Platform as a Service (PaaS)',
        'Software as a Service (SaaS)',
      ],
      icon: 'fas fa-cloud',
    },
    {
      id: 4,
      title: 'IT-Sicherheit',
      description:
        'Umfassende Sicherheitslösungen zum Schutz Ihrer Daten und Systeme.',
      features: [
        'Sicherheitsaudits und Penetrationstests',
        'Implementierung von Sicherheitsmaßnahmen',
        'Schulungen für Mitarbeiter',
        'Notfallwiederherstellung und Backup-Lösungen',
      ],
      icon: 'fas fa-shield-alt',
    },
    {
      id: 5,
      title: 'Netzwerkmanagement',
      description:
        'Professionelle Verwaltung und Optimierung Ihrer Netzwerkinfrastruktur.',
      features: [
        'Netzwerkdesign und -implementierung',
        'Verwaltung von Netzwerkgeräten',
        'Überwachung und Fehlerbehebung',
        'Leistungsoptimierung',
      ],
      icon: 'fas fa-network-wired',
    },
  ];

  const carouselRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState(0);

  useEffect(() => {
    if (carouselRef.current) {
      setWidth(
        carouselRef.current.scrollWidth - carouselRef.current.offsetWidth
      );
    }
  }, []);

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className="overflow-x-hidden"
      exit={{ opacity: 0 }}
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div
        className="bg-center bg-cover py-32 text-center text-white sm:py-40"
        style={{
          backgroundImage:
            "linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('/images/services-bg.jpg')",
        }}
      >
        <div className="container mx-auto px-4">
          <motion.h1
            animate={{ y: 0, opacity: 1 }}
            className="mb-4 font-bold text-4xl sm:text-5xl"
            initial={{ y: -50, opacity: 0 }}
            transition={{ delay: 0.2, duration: 0.8 }}
          >
            Unsere Leistungen
          </motion.h1>
          <motion.p
            animate={{ y: 0, opacity: 1 }}
            className="mx-auto max-w-2xl text-lg opacity-90 sm:text-xl"
            initial={{ y: 50, opacity: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            Entdecken Sie unser umfassendes Angebot an IT-Dienstleistungen
          </motion.p>
        </div>
      </div>

      <section className="py-16 sm:py-20">
        <div className="container mx-auto">
          <div className="relative w-full overflow-hidden py-4">
            <motion.div
              className="flex cursor-grab gap-4 p-4 active:cursor-grabbing sm:gap-6 md:gap-8"
              drag="x"
              dragConstraints={{ left: -width, right: 0 }}
              ref={carouselRef}
            >
              {services.map((service) => (
                <motion.div
                  className="w-[90vw] flex-shrink-0 sm:w-[80vw] md:w-[450px]"
                  key={service.id}
                >
                  <GlowCard
                    centerIcon={true}
                    iconClass={service.icon}
                    title={service.title}
                  >
                    <div className="p-0">
                      <p className="mb-6 text-text-color/80 leading-relaxed">
                        {service.description}
                      </p>
                      <ul className="space-y-3 text-sm">
                        {service.features.map((feature) => (
                          <li className="flex items-center" key={feature}>
                            <i className="fas fa-check mr-3 text-primary" />{' '}
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </GlowCard>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      <section className="bg-background/50 py-16 sm:py-20">
        <div className="container mx-auto px-4">
          <ScrollAnimation>
            <div className="rounded-lg bg-card-background p-8 text-center shadow-lg md:p-12">
              <h2 className="mb-6 font-bold text-2xl text-white md:text-3xl">
                Maßgeschneiderte IT-Lösungen für Ihr Unternehmen
              </h2>
              <p className="mx-auto mb-8 max-w-3xl text-text-color/80">
                Unsere Experten beraten Sie gerne zu Ihren individuellen
                Anforderungen und entwickeln gemeinsam mit Ihnen die optimale
                IT-Strategie für Ihr Unternehmen.
              </p>
              <Link
                className="inline-block rounded-md bg-primary px-8 py-3 font-semibold text-white transition-colors hover:bg-secondary"
                to="/kontakt"
              >
                Kontakt aufnehmen
              </Link>
            </div>
          </ScrollAnimation>
        </div>
      </section>
    </motion.div>
  );
};

export default Leistungen;
