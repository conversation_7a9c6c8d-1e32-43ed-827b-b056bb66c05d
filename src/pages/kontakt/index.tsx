import { useMutation } from 'convex/react';
import { motion } from 'framer-motion';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { ScrollAnimation } from '@/components/common';
import { useCookieConsent, useToast } from '@/components/context';
import { ConsentWrapper } from '@/components/cookies';
import { GlowCard } from '@/components/ui';
import { api } from '../../../convex/_generated/api';

const Kontakt = () => {
  const { setShowBanner } = useCookieConsent();
  const { showToast } = useToast();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    betreff: '',
    nachricht: '',
    zustimmungGegeben: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const kontaktSenden = useMutation(api.kontaktformular.database.send);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    const target = e.target as HTMLInputElement;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? target.checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.zustimmungGegeben) {
      showToast('Bitte stimmen Sie der Datenschutzerklärung zu.', 'warning');
      return;
    }
    setIsSubmitting(true);
    try {
      await kontaktSenden({
        ...formData,
        zustimmung: formData.zustimmungGegeben,
      });
      showToast(
        'Vielen Dank! Ihre Nachricht wurde erfolgreich übermittelt.',
        'success'
      );
      setFormData({
        name: '',
        email: '',
        betreff: '',
        nachricht: '',
        zustimmungGegeben: false,
      });
    } catch (error) {
      console.error('Fehler beim Speichern der Formulardaten:', error);
      showToast('Es gab ein Problem beim Senden Ihrer Nachricht.', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className="overflow-x-hidden"
      exit={{ opacity: 0 }}
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div
        className="bg-center bg-cover py-32 text-center text-white sm:py-40"
        style={{
          backgroundImage:
            "linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('/images/contact-bg.jpg')",
        }}
      >
        <div className="container mx-auto px-4">
          <motion.h1
            animate={{ y: 0, opacity: 1 }}
            className="mb-4 font-bold text-4xl sm:text-5xl"
            initial={{ y: -50, opacity: 0 }}
            transition={{ delay: 0.2, duration: 0.8 }}
          >
            Kontakt
          </motion.h1>
          <motion.p
            animate={{ y: 0, opacity: 1 }}
            className="mx-auto max-w-2xl text-lg opacity-90 sm:text-xl"
            initial={{ y: 50, opacity: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            Wir freuen uns auf Ihre Nachricht
          </motion.p>
        </div>
      </div>

      <section className="bg-background/30 py-16 sm:py-20">
        <div className="container mx-auto px-4">
          <div className="mt-8 grid gap-12 lg:grid-cols-5">
            <div className="lg:col-span-2">
              <ScrollAnimation>
                <GlowCard className="p-0">
                  <div className="p-8">
                    <h2 className="mb-6 border-primary/20 border-b-2 pb-3 font-bold text-2xl text-primary">
                      Kontaktdaten
                    </h2>
                    <div className="space-y-6">
                      <ContactInfoItem
                        href="mailto:<EMAIL>"
                        icon="fas fa-envelope"
                        text="<EMAIL>"
                        title="E-Mail"
                      />
                      <ContactInfoItem
                        href="tel:+*************"
                        icon="fas fa-phone-alt"
                        text="+49 2161 9850066"
                        title="Telefon"
                      />
                      <ContactInfoItem
                        icon="fas fa-map-marker-alt"
                        title="Adresse"
                      >
                        <ConsentWrapper
                          fallback={<AddressPlaceholder />}
                          type="external"
                        >
                          <a
                            className="transition-colors hover:text-primary"
                            href="https://maps.google.com/?q=Schulstraße+60,41065+Mönchengladbach"
                            rel="noopener noreferrer"
                            target="_blank"
                          >
                            Schulstraße 60
                            <br />
                            41065 Mönchengladbach
                          </a>
                        </ConsentWrapper>
                      </ContactInfoItem>
                      <ContactInfoItem
                        icon="fas fa-clock"
                        text={
                          <>
                            Mo. - Fr.: 08:00 - 18:00 Uhr
                            <br />
                            24/7 Notfall-Support für Vertragskunden
                          </>
                        }
                        title="Geschäftszeiten"
                      />
                    </div>
                  </div>
                </GlowCard>
              </ScrollAnimation>
            </div>

            <div className="lg:col-span-3">
              <ScrollAnimation>
                <GlowCard className="p-0">
                  <div className="p-8">
                    <h2 className="mb-6 border-primary/20 border-b-2 pb-3 font-bold text-2xl text-primary">
                      Schreiben Sie uns
                    </h2>
                    <form className="space-y-6" onSubmit={handleSubmit}>
                      <div className="grid gap-6 sm:grid-cols-2">
                        <FormInput
                          icon="fas fa-user"
                          name="name"
                          onChange={handleChange}
                          placeholder="Ihr Name"
                          required
                          value={formData.name}
                        />
                        <FormInput
                          icon="fas fa-envelope"
                          name="email"
                          onChange={handleChange}
                          placeholder="Ihre E-Mail-Adresse"
                          required
                          type="email"
                          value={formData.email}
                        />
                      </div>
                      <FormInput
                        icon="fas fa-pen"
                        name="betreff"
                        onChange={handleChange}
                        placeholder="Betreff Ihrer Nachricht"
                        required
                        value={formData.betreff}
                      />
                      <FormTextarea
                        icon="fas fa-comment-alt"
                        name="nachricht"
                        onChange={handleChange}
                        placeholder="Ihre Nachricht an uns"
                        required
                        value={formData.nachricht}
                      />
                      <div className="flex items-start space-x-3">
                        <input
                          checked={formData.zustimmungGegeben}
                          className="mt-1 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                          id="zustimmungGegeben"
                          name="zustimmungGegeben"
                          onChange={handleChange}
                          required
                          type="checkbox"
                        />
                        <label
                          className="text-sm text-text-color/80"
                          htmlFor="zustimmungGegeben"
                        >
                          Ich habe die{' '}
                          <Link
                            className="text-primary hover:underline"
                            to="/datenschutz"
                          >
                            Datenschutzerklärung
                          </Link>{' '}
                          gelesen und stimme der Verarbeitung meiner Daten zu.
                        </label>
                      </div>
                      <button
                        className="flex w-full transform items-center justify-center gap-3 rounded-lg bg-primary px-4 py-3 font-semibold text-white shadow-md transition-all duration-300 hover:scale-105 hover:bg-secondary disabled:cursor-not-allowed disabled:opacity-70"
                        disabled={isSubmitting}
                        type="submit"
                      >
                        {isSubmitting ? (
                          <>
                            <i className="fas fa-spinner fa-spin" /> Senden...
                          </>
                        ) : (
                          <>
                            <i className="fas fa-paper-plane" /> Nachricht
                            senden
                          </>
                        )}
                      </button>
                    </form>
                  </div>
                </GlowCard>
              </ScrollAnimation>
            </div>
          </div>
        </div>
      </section>

      <section className="bg-background/30 py-16 sm:py-20">
        <div className="container mx-auto px-4">
          <ScrollAnimation>
            <ConsentWrapper
              fallback={
                <div className="flex min-h-[200px] flex-col items-center justify-center rounded-xl bg-card-background p-8 text-center">
                  <p className="mb-4 text-lg">
                    Bitte stimmen Sie externen Inhalten zu, um die Karte
                    anzuzeigen.
                  </p>
                  <button
                    className="flex items-center gap-2 rounded-lg bg-primary px-6 py-2 font-semibold text-white transition-colors hover:bg-secondary"
                    onClick={() => setShowBanner(true)}
                    type="button"
                  >
                    <i className="fas fa-cookie-bite" /> Cookie-Einstellungen
                  </button>
                </div>
              }
              type="external"
            >
              <GlowCard className="!p-0 overflow-hidden border-0 shadow-2xl">
                <iframe
                  allowFullScreen={false}
                  height="450"
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2501.931212189274!2d6.***************!3d51.18089327174135!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47b8acfe63079e69%3A0xd41c8f68e8662f5a!2sSchulstra%C3%9Fe%2060%2C%2041065%20M%C3%B6nchengladbach!5e0!3m2!1sde!2sde!4v1723621616713!5m2!1sde!2sde"
                  style={{ border: 0 }}
                  title="Google Maps Standort Innov8 IT GmbH"
                  width="100%"
                />
              </GlowCard>
            </ConsentWrapper>
          </ScrollAnimation>
        </div>
      </section>
    </motion.div>
  );
};

// Helper components to reduce repetition
const ContactInfoItem: React.FC<{
  icon: string;
  title: string;
  href?: string;
  text?: React.ReactNode;
  children?: React.ReactNode;
}> = ({ icon, title, href, text, children }) => (
  <div className="flex items-start gap-4">
    <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-primary/10 text-primary text-xl transition-transform group-hover:scale-110">
      <i className={icon} />
    </div>
    <div>
      <h3 className="mb-1 font-semibold text-lg">{title}</h3>
      {href ? (
        <a
          className="text-text-color/80 transition-colors hover:text-primary"
          href={href}
        >
          {text}
        </a>
      ) : (
        <p className="text-text-color/80">{text}</p>
      )}
      {children}
    </div>
  </div>
);

const AddressPlaceholder = () => (
  <p className="text-text-color/70">
    Schulstraße 60
    <br />
    41065 Mönchengladbach
  </p>
);

const FormInput: React.FC<
  React.InputHTMLAttributes<HTMLInputElement> & { icon: string }
> = ({ icon, ...props }) => (
  <div className="relative">
    <i
      className={`${icon} -translate-y-1/2 absolute top-1/2 left-4 text-primary`}
    />
    <input
      {...props}
      className="w-full rounded-lg border border-border-color bg-background/50 py-3 pr-4 pl-12 text-white placeholder-text-color/60 outline-none transition-all focus:border-primary focus:ring-2 focus:ring-primary"
    />
  </div>
);

const FormTextarea: React.FC<
  React.TextareaHTMLAttributes<HTMLTextAreaElement> & { icon: string }
> = ({ icon, ...props }) => (
  <div className="relative">
    <i className={`${icon} absolute top-5 left-4 text-primary`} />
    <textarea
      {...props}
      className="resize-vertical w-full rounded-lg border border-border-color bg-background/50 py-3 pr-4 pl-12 text-white placeholder-text-color/60 outline-none transition-all focus:border-primary focus:ring-2 focus:ring-primary"
      rows={5}
    />
  </div>
);

export default Kontakt;
