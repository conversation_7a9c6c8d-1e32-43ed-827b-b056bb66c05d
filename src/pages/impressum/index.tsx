import { motion } from 'framer-motion';
import { ScrollAnimation } from '@/components/common';

const Impressum = () => {
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className="overflow-x-hidden"
      exit={{ opacity: 0 }}
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div
        className="bg-center bg-cover py-32 text-center text-white sm:py-40"
        style={{
          backgroundImage:
            "linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('/images/imprint-bg.jpg')",
        }}
      >
        <div className="container mx-auto px-4">
          <motion.h1
            animate={{ y: 0, opacity: 1 }}
            className="mb-4 font-bold text-4xl sm:text-5xl"
            initial={{ y: -50, opacity: 0 }}
            transition={{ delay: 0.2, duration: 0.8 }}
          >
            Impressum
          </motion.h1>
          <motion.p
            animate={{ y: 0, opacity: 1 }}
            className="mx-auto max-w-2xl text-lg opacity-90 sm:text-xl"
            initial={{ y: 50, opacity: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            Angaben gemäß § 5 TMG
          </motion.p>
        </div>
      </div>

      <section className="py-16 sm:py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-3xl text-text-color/90">
            <ScrollAnimation>
              <h2 className="mt-10 mb-6 font-bold text-2xl text-primary sm:text-3xl">
                Angaben gemäß § 5 TMG
              </h2>
              <p className="leading-relaxed">
                innov8-IT - Kara & Labitzki GbR
                <br />
                St.-IdNr.: 121/5825/6142
              </p>
            </ScrollAnimation>

            <ScrollAnimation>
              <h2 className="mt-10 mb-6 font-bold text-2xl text-primary sm:text-3xl">
                Vertreten durch
              </h2>
              <p className="leading-relaxed">
                Yusuf Kara & Sven Labitzki
                <br />
                Schulstraße 60, 41065 Mönchengladbach
              </p>
            </ScrollAnimation>

            <ScrollAnimation>
              <h2 className="mt-10 mb-6 font-bold text-2xl text-primary sm:text-3xl">
                Kontakt
              </h2>
              <p className="leading-relaxed">
                Telefon: +49 2161 9850066
                <br />
                E-Mail:{' '}
                <a
                  className="text-primary hover:underline"
                  href="mailto:<EMAIL>"
                >
                  <EMAIL>
                </a>
              </p>
            </ScrollAnimation>

            <ScrollAnimation>
              <h2 className="mt-10 mb-6 font-bold text-2xl text-primary sm:text-3xl">
                Haftung für Inhalte
              </h2>
              <p className="leading-relaxed">
                Als Diensteanbieter sind wir gemäß § 7 Abs.1 TMG für eigene
                Inhalte auf diesen Seiten nach den allgemeinen Gesetzen
                verantwortlich. Nach §§ 8 bis 10 TMG sind wir als
                Diensteanbieter jedoch nicht verpflichtet, übermittelte oder
                gespeicherte fremde Informationen zu überwachen oder nach
                Umständen zu forschen, die auf eine rechtswidrige Tätigkeit
                hinweisen.
              </p>
            </ScrollAnimation>

            <ScrollAnimation>
              <h2 className="mt-10 mb-6 font-bold text-2xl text-primary sm:text-3xl">
                Haftung für Links
              </h2>
              <p className="leading-relaxed">
                Unser Angebot enthält Links zu externen Websites Dritter, auf
                deren Inhalte wir keinen Einfluss haben. Deshalb können wir für
                diese fremden Inhalte auch keine Gewähr übernehmen. Für die
                Inhalte der verlinkten Seiten ist stets der jeweilige Anbieter
                oder Betreiber der Seiten verantwortlich. Die verlinkten Seiten
                wurden zum Zeitpunkt der Verlinkung auf mögliche Rechtsverstöße
                überprüft. Rechtswidrige Inhalte waren zum Zeitpunkt der
                Verlinkung nicht erkennbar.
              </p>
            </ScrollAnimation>
          </div>
        </div>
      </section>
    </motion.div>
  );
};

export default Impressum;
