import { useCallback, useEffect, useState } from 'react';
import { PartnerLogos } from '@/components/pages/home';
import { GlowCard } from '@/components/ui';

const Home = () => {
  return (
    <div className="overflow-x-hidden">
      {/* Hero Section */}
      <div className="relative flex h-screen items-center justify-center text-center">
        <div className="mx-auto max-w-3xl px-4">
          <h1 className="mb-4 text-center font-extrabold text-5xl md:text-7xl">
            <div className="inline-flex items-center justify-center">
              <div className="relative inline-block px-2 pb-2">
                <div className="flex items-center justify-center">
                  <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent">
                    innov
                  </span>
                  <span className="relative mx-1 inline-block text-6xl text-blue-400 md:text-8xl">
                    <span className="relative z-10">8</span>
                    <span
                      className="absolute top-0 left-0 animate-pulse-glow text-blue-400"
                      style={{ textShadow: '0 0 15px rgba(74, 144, 226, 0.8)' }}
                    >
                      8
                    </span>
                  </span>
                  <span className="bg-gradient-to-r from-blue-400 to-blue-600 bg-clip-text text-transparent">
                    -IT
                  </span>
                </div>
                <div className="absolute bottom-0 left-0 h-0.5 w-full bg-gradient-to-r from-transparent via-blue-400 to-transparent" />
              </div>
            </div>
          </h1>
          <p className="mt-4 mb-8 font-light text-blue-400 text-xl tracking-wider md:text-2xl">
            <PixelTextEffect
              delay={0}
              finalText="Innovative IT-Lösungen"
              initialText="innov8-IT Lösungen"
            />
          </p>
        </div>
      </div>

      {/* Why Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center font-bold text-3xl text-white md:text-4xl">
            Warum innov<span className="text-blue-400">8</span>-IT?
          </h2>
          <div className="mt-14 grid gap-8 md:grid-cols-3">
            <GlowCard
              centerIcon={true}
              iconClass="fas fa-rocket"
              title="Innovative Lösungen"
            >
              <p className="text-sm text-text-color/80 leading-relaxed">
                Wir entwickeln maßgeschneiderte IT-Lösungen, die Ihr Unternehmen
                voranbringen.
              </p>
              <ul className="mt-4 space-y-3 text-sm">
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" />{' '}
                  Zukunftssichere Technologien
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" /> Individuelle
                  Anpassungen
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" />{' '}
                  Benutzerfreundliche Oberflächen
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" />{' '}
                  Kontinuierliche Verbesserungen
                </li>
              </ul>
            </GlowCard>

            <GlowCard
              centerIcon={true}
              iconClass="fas fa-shield-alt"
              title="Höchste Sicherheit"
            >
              <p className="text-sm text-text-color/80 leading-relaxed">
                Ihre Daten sind bei uns sicher – wir setzen auf modernste
                Sicherheitsstandards.
              </p>
              <ul className="mt-4 space-y-3 text-sm">
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" />{' '}
                  Verschlüsselte Datenübertragung
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" /> Regelmäßige
                  Sicherheitsaudits
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" />{' '}
                  DSGVO-Konformität
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" />{' '}
                  Backup-Strategien
                </li>
              </ul>
            </GlowCard>

            <GlowCard
              centerIcon={true}
              iconClass="fas fa-headset"
              title="Persönlicher Support"
            >
              <p className="text-sm text-text-color/80 leading-relaxed">
                Unser Support-Team steht Ihnen jederzeit mit Rat und Tat zur
                Seite.
              </p>
              <ul className="mt-4 space-y-3 text-sm">
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" /> Schnelle
                  Reaktionszeiten
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" /> Kompetente
                  Beratung
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" /> Remote- und
                  Vor-Ort-Support
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check mr-3 text-primary" /> Regelmäßige
                  Wartung
                </li>
              </ul>
            </GlowCard>
          </div>
        </div>
      </section>

      {/* Partner Logos Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <PartnerLogos />
        </div>
      </section>
    </div>
  );
};

const PixelTextEffect = ({
  initialText,
  finalText,
  delay = 0,
}: {
  initialText: string;
  finalText: string;
  delay?: number;
}) => {
  const [displayText, setDisplayText] = useState(initialText);
  const [isAnimating, setIsAnimating] = useState(false);

  const animateText = useCallback(() => {
    setIsAnimating(true);

    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
    const initialWords = initialText.split(' ');
    let step = 0;
    const maxSteps = 20;

    const interval = setInterval(() => {
      step++;

      if (step < maxSteps) {
        const glitchText = initialWords[0]
          .split('')
          .map((_: string, i: number) => {
            const keepOriginal = Math.random() < step / maxSteps;
            if (keepOriginal && i < initialWords[0].length) {
              return initialWords[0][i];
            }
            return chars.charAt(Math.floor(Math.random() * chars.length));
          })
          .join('');

        const newText = [glitchText, ...initialWords.slice(1)].join(' ');
        setDisplayText(newText);
      } else {
        setDisplayText(finalText);
        clearInterval(interval);
        setIsAnimating(false);
      }
    }, 80);
  }, [finalText, initialText]);

  useEffect(() => {
    const timer = setTimeout(() => {
      animateText();
    }, delay * 1000);

    return () => clearTimeout(timer);
  }, [delay, animateText]);

  return (
    <span
      className={`relative inline-block ${
        isAnimating
          ? "after:absolute after:top-0 after:left-0 after:h-full after:w-full after:animate-glitch-bg after:bg-gradient-to-r after:from-blue-400/10 after:via-blue-400/20 after:to-blue-400/10 after:content-['']"
          : ''
      }`}
    >
      {displayText}
    </span>
  );
};

export default Home;
