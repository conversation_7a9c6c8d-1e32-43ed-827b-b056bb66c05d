modules = ["nodejs-20"]

[nix]
channel = "stable-24_05"

[workflows]
runButton = "DEV"

[[workflows.workflow]]
name = "DEV"
author = 2119567
mode = "sequential"

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"

[[workflows.workflow]]
name = "BUILD"
author = 2119567
mode = "sequential"

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run build"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run start"

[deployment]
build = "npm run build"
deploymentTarget = "cloudrun"
run = ["sh", "-c", "npm run start"]

[[ports]]
localPort = 5173
externalPort = 80
