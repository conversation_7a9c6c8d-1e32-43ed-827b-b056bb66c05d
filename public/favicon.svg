<svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512">
    <defs>
        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#2c3e50" />
            <stop offset="100%" stop-color="#1a2530" />
        </linearGradient>

        <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#3498db" />
            <stop offset="100%" stop-color="#2ecc71" />
        </linearGradient>

        <filter id="glow" x="-30%" y="-30%" width="160%" height="160%">
            <feGaussianBlur stdDeviation="12" result="blur" />
            <feFlood flood-color="#3498db" flood-opacity="0.3" result="color" />
            <feComposite in="color" in2="blur" operator="in" result="glow" />
            <feComposite in="glow" in2="SourceGraphic" operator="over" />
        </filter>
    </defs>

    <!-- Background -->
    <rect x="0" y="0" width="512" height="512" rx="128" fill="url(#bgGradient)" />

    <!-- Abstract 8 Design - Infinity-inspired -->
    <g filter="url(#glow)">
        <!-- Upper Loop -->
        <path d="M256,96 
             C192,96 160,128 160,176 
             C160,224 192,256 256,256 
             C320,256 352,224 352,176 
             C352,128 320,96 256,96 Z" fill="none" stroke="url(#accentGradient)" stroke-width="32"
            stroke-linecap="round" />

        <!-- Lower Loop -->
        <path d="M256,256 
             C192,256 160,288 160,336 
             C160,384 192,416 256,416 
             C320,416 352,384 352,336 
             C352,288 320,256 256,256 Z" fill="none" stroke="url(#accentGradient)" stroke-width="32"
            stroke-linecap="round" />
    </g>

    <!-- Digital Accent Elements -->
    <circle cx="160" cy="176" r="16" fill="#3498db" opacity="0.8" />
    <circle cx="352" cy="336" r="16" fill="#2ecc71" opacity="0.8" />

    <!-- Tech Dots -->
    <circle cx="128" cy="128" r="12" fill="#ffffff" opacity="0.5" />
    <circle cx="384" cy="128" r="12" fill="#ffffff" opacity="0.5" />
    <circle cx="128" cy="384" r="12" fill="#ffffff" opacity="0.5" />
    <circle cx="384" cy="384" r="12" fill="#ffffff" opacity="0.5" />

    <!-- Central Innovation Point -->
    <circle cx="256" cy="256" r="24" fill="#ffffff" />
</svg>