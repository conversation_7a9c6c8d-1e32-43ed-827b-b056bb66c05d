{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "extends": ["ultracite"], "linter": {"rules": {"suspicious": {"noConsole": "off", "noExplicitAny": "off", "noAssignInExpressions": "off"}, "style": {"useFilenamingConvention": "off", "useDefaultSwitchClause": "off", "useBlockStatements": "off", "noParameterAssign": "off"}, "performance": {"noImgElement": "off"}, "complexity": {"noExcessiveCognitiveComplexity": "off", "noForEach": "off"}, "nursery": {"noAwaitInLoop": "off", "noShadow": "off"}}}}