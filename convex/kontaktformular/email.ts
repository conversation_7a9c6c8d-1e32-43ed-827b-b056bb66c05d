// Definiert die Node.js-Laufzeitumgebung für diese Datei
'use node';

import { v } from 'convex/values';
import nodemailer from 'nodemailer';
import { action } from '../_generated/server';

// --- Action zum Senden der E-Mail ---
export const send = action({
  args: {
    name: v.string(),
    email: v.string(),
    subject: v.string(),
    message: v.string(),
  },
  handler: async (_ctx, args) => {
    // Umgebungsvariablen aus Convex Dashboard laden
    const emailHost = process.env.EMAIL_HOST;
    const emailPort = process.env.EMAIL_PORT;
    const emailUser = process.env.EMAIL_USER;
    const emailPassword = process.env.EMAIL_PASSWORD;
    const emailFrom = process.env.EMAIL_FROM;
    const emailTo = process.env.EMAIL_TO;
    const emailSecure = process.env.EMAIL_SECURE === 'true';

    if (
      !(
        emailHost &&
        emailPort &&
        emailUser &&
        emailPassword &&
        emailFrom &&
        emailTo
      )
    ) {
      throw new Error(
        'E-Mail-Konfigurationsvariablen sind im Convex Dashboard nicht vollständig gesetzt.'
      );
    }

    // Nodemailer Transporter erstellen
    const transporter = nodemailer.createTransport({
      host: emailHost,
      port: Number.parseInt(emailPort, 10),
      secure: emailSecure,
      auth: {
        user: emailUser,
        pass: emailPassword,
      },
    });

    // Aktuelles Datum formatieren
    const currentDate = new Date().toLocaleDateString('de-DE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

    // HTML-E-Mail-Formatierung
    const emailHtml = `
      <!DOCTYPE html>
      <html lang="de">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Neue Kontaktanfrage</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; color: #333; background-color: #f7f7f7;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);">
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #4a90e2, #3498db); padding: 30px 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 24px; font-weight: 600;">Neue Kontaktanfrage</h1>
            <p style="color: rgba(255, 255, 255, 0.9); margin: 10px 0 0 0; font-size: 16px;">Über das Formular auf Ihrer Website</p>
          </div>
          
          <!-- Content -->
          <div style="padding: 30px 20px;">
            <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5; color: #666;">Am <strong>${currentDate} Uhr</strong> wurde folgende Anfrage über das Kontaktformular versendet:</p>
            
            <h2 style="color: #4a90e2; margin: 25px 0 15px 0; font-size: 18px; font-weight: 600;">Kontaktinformationen:</h2>
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 25px;">
              <tr>
                <td style="padding: 15px; background-color: #f2f7fd; border-left: 4px solid #4a90e2; font-weight: 600; width: 120px;">Name</td>
                <td style="padding: 15px; border-bottom: 1px solid #eee;">${args.name}</td>
              </tr>
              <tr>
                <td style="padding: 15px; background-color: #f2f7fd; border-left: 4px solid #4a90e2; font-weight: 600;">E-Mail</td>
                <td style="padding: 15px; border-bottom: 1px solid #eee;">
                  <a href="mailto:${args.email}" style="color: #4a90e2; text-decoration: none;">${args.email}</a>
                </td>
              </tr>
              <tr>
                <td style="padding: 15px; background-color: #f2f7fd; border-left: 4px solid #4a90e2; font-weight: 600;">Betreff</td>
                <td style="padding: 15px; border-bottom: 1px solid #eee;">${args.subject}</td>
              </tr>
            </table>
            
            <h2 style="color: #4a90e2; margin: 25px 0 15px 0; font-size: 18px; font-weight: 600;">Nachricht:</h2>
            <div style="background-color: #f9f9f9; border-radius: 6px; padding: 20px; line-height: 1.6; color: #444; border-left: 4px solid #4a90e2;">
              ${args.message.replace(/\n/g, '<br>')}
            </div>
            
            <div style="margin-top: 30px; background-color: #f2f7fd; padding: 20px; border-radius: 6px;">
              <p style="margin: 0 0 10px 0; font-weight: 600; color: #444;">Schnellantwort:</p>
              <a href="mailto:${args.email}?subject=Re: ${args.subject}" style="display: inline-block; background-color: #4a90e2; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; font-weight: 500;">Auf diese E-Mail antworten</a>
            </div>
          </div>
          
          <!-- Footer -->
          <div style="background-color: #f2f2f2; padding: 20px; text-align: center; color: #666; font-size: 14px; border-top: 1px solid #e5e5e5;">
            <p style="margin: 0 0 10px 0;">Diese E-Mail wurde über das Kontaktformular versendet.</p>
            <p style="margin: 0;">© ${new Date().getFullYear()} — innov8-IT</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // E-Mail Optionen
    const mailOptions = {
      from: `"Kontaktformular innov8-IT" <${emailFrom}>`,
      to: emailTo,
      replyTo: args.email,
      subject: `${args.subject}`,
      text: `Neue Kontaktanfrage erhalten:\nName: ${args.name}\nEmail: ${args.email}\nBetreff: ${args.subject}\nNachricht:\n${args.message}`,
      html: emailHtml,
    };

    try {
      // E-Mail senden
      const info = await transporter.sendMail(mailOptions);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      throw new Error(`Fehler beim Senden der E-Mail: ${error}`);
    }
  },
});
