import { v } from 'convex/values';
import { api } from '../_generated/api';
import { mutation } from '../_generated/server';

// --- Mutation zum Speichern & Benachrichtigen ---
export const send = mutation({
  args: {
    name: v.string(),
    email: v.string(),
    betreff: v.string(),
    nachricht: v.string(),
    zustimmung: v.boolean(),
  },
  handler: async (ctx, args) => {
    // Daten speichern
    const anfrageId = await ctx.db.insert('kontaktAnfragen', {
      name: args.name,
      email: args.email,
      betreff: args.betreff,
      nachricht: args.nachricht,
      zustimmung: args.zustimmung,
    });

    // "E-Mail-Versand-function" aufrufen
    await ctx.scheduler.runAfter(0, api.kontaktformular.email.send, {
      name: args.name,
      email: args.email,
      subject: args.betreff,
      message: args.nachricht,
    });

    return anfrageId;
  },
});
