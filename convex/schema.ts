import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  // Tabelle für Kontaktanfragen
  kontaktAnfragen: defineTable({
    name: v.string(),
    email: v.string(),
    betreff: v.string(),
    nachricht: v.string(),
    zustimmung: v.boolean(),
  }).index('nach_email', ['email']),

  // Hier können später weitere Tabellen hinzugefügt werden
  // z.B.:
  // users: defineTable({...}),
  // products: defineTable({...}),
});
