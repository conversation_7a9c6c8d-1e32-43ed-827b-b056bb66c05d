---
type: "agent_requested"
---

# Toast-Benachrichtigungssystem

Das Toast-System liefert konsistente Erfolg-, <PERSON><PERSON>-, Warn- und Info-Meldungen über alle Seiten hinweg.

## Architektur

| Ebene | Datei | Aufgabe |
|-------|-------|---------|
| Context | [`src/components/context/ToastContext.tsx`](mdc:src/components/context/ToastContext.tsx) | Globale Verwaltung der Toast-Liste |
| Container | [`src/components/ui/ToastContainer.tsx`](mdc:src/components/ui/ToastContainer.tsx) | Rendert & animiert aktive Toasts |
| Einzel-Toast | [`src/components/ui/Toast.tsx`](mdc:src/components/ui/Toast.tsx) | Darstellung einer Meldung |

## Typen & Interfaces

```typescript
export type ToastType = 'success' | 'error' | 'warning' | 'info';

interface ToastData {
  id: string;
  message: string;
  type: ToastType;
}

interface ToastContextType {
  showToast(msg: string, type: ToastType): void;
  hideToast(id: string): void;
}
```

## Verwendung

```typescript
import { useToast } from "@/components/context";

const { showToast } = useToast();

showToast("Ihre Nachricht wurde gesendet!", "success");
```

### Hook

```typescript
export const useToast = () => {
  const ctx = React.useContext(ToastContext);
  if (!ctx) throw new Error('useToast must be used within a ToastProvider');
  return ctx;
};
```

## Lebenszyklus

1. `showToast` erzeugt ein `ToastData`-Objekt (uuid).  
2. Container rendert neues Toast mit Einblend-Animation.  
3. Timeout (standard 4 s) löst `hideToast`.  
4. Ausblend-Animation, danach Entfernung aus State.

## Styling

```css
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1200;
}
.toast {
  display: flex;
  gap: .75rem;
  padding: .75rem 1.25rem;
  border-radius: .5rem;
  box-shadow: 0 4px 12px rgba(0,0,0,.15);
  animation: fade-in .25s ease-out;
}
.toast-success   { background: #38a169; color: #fff; }
.toast-error     { background: #e53e3e; color: #fff; }
.toast-warning   { background: #dd6b20; color: #fff; }
.toast-info      { background: #3182ce; color: #fff; }
```

## Best-Practices

- **Maximal 3 gleichzeitige Toasts**. Bei Überschreitung älteste entfernen.  
- Icons via FontAwesome für schnelle visuelle Zuordnung.  
- Texte kurz halten (< 80 Zeichen).  
- Für längere Infos besser Modal/Dialog verwenden.

## Erweiterung

Neue Toast-Typen hinzufügen:

1. `ToastType` um Wert ergänzen.  
2. Farbklasse in CSS + Icon in Komponente.  
3. Optional: spezifische Auto-Hide-Zeit konfigurieren.

## Testing-Checkliste

- [ ] Toast erscheint bei `showToast` sofort ✅  
- [ ] Richtiger Stil pro Typ ✅  
- [ ] Auto-Hide entfernt Toast nach Zeit ✅  
- [ ] Manueller Close-Button funktioniert ✅  
- [ ] Keine Memory-Leaks (Cleanup in `useEffect`) ✅  

---
Letzte Aktualisierung: 29.05.2025