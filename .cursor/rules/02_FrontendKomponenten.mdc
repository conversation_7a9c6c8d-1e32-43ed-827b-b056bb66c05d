---
description: Frontend-Komponenten, Seiten
globs: 
alwaysApply: false
---
# Frontend-Komponenten & Seitenarchitektur

Dieses Dokument beschreibt die Struktur der React-Seiten, aller wiederverwendbaren UI-Komponenten sowie begleitender Context-Provider der innov8-IT Website.

## Seiten (Pages)

Die Hauptseiten befinden sich in `src/pages/[seitenname]/index.tsx` und werden über React Router gerendert. Übergänge erfolgen mit Framer Motion.

| Route | Komponente | Zweck |
|-------|------------|-------|
| `/` | [`src/pages/home/<USER>/pages/home/<USER>
| `/leistungen` | [`src/pages/leistungen/index.tsx`](mdc:src/pages/leistungen/index.tsx) | Detailübersicht der angebotenen IT-Services |
| `/kontakt` | [`src/pages/kontakt/index.tsx`](mdc:src/pages/kontakt/index.tsx) | Kontaktformular & Unternehmensinfos |
| `/impressum` | [`src/pages/impressum/index.tsx`](mdc:src/pages/impressum/index.tsx) | Rechtliche Informationen |
| `/datenschutz` | [`src/pages/datenschutz/index.tsx`](mdc:src/pages/datenschutz/index.tsx) | Datenschutzerklärung |

Alle Routen sind in [`src/App.tsx`](mdc:src/App.tsx) registriert.

## Wiederverwendbare UI-Komponenten

Die Components sind jetzt logisch in Unterordner organisiert:

### Layout Components (`src/components/layout/`)
- **[`Navbar.tsx`](mdc:src/components/layout/Navbar.tsx)** – Responsive Navigation mit Burger-Menu  
- **[`Footer.tsx`](mdc:src/components/layout/Footer.tsx)** – Footer mit Kontakt- & Rechtlinks  
- **[`PageTransition.tsx`](mdc:src/components/layout/PageTransition.tsx)** – Framer Motion Seitenübergänge  

### UI Components (`src/components/ui/`)
- **[`GlowCard.tsx`](mdc:src/components/ui/GlowCard.tsx)** – Karte mit Glow-Hover-Effekt  
- **[`Toast.tsx`](mdc:src/components/ui/Toast.tsx)** – Einzelne Toast-Meldung  
- **[`ToastContainer.tsx`](mdc:src/components/ui/ToastContainer.tsx)** – Meldungscontainer  

### Cookie Components (`src/components/cookies/`)
- **[`Banner.tsx`](mdc:src/components/cookies/Banner.tsx)** – DSGVO-Banner  
- **[`Button.tsx`](mdc:src/components/cookies/Button.tsx)** – Schwebender Button zum Öffnen des Banners  
- **[`ConsentWrapper.tsx`](mdc:src/components/cookies/ConsentWrapper.tsx)** – Wrapper für externe Inhalte  

### Common Components (`src/components/common/`)
- **[`ScrollAnimation.tsx`](mdc:src/components/common/ScrollAnimation.tsx)** – Intersection-Observer basierte Scroll-Animationen  
- **[`ErrorBoundary.tsx`](mdc:src/components/common/ErrorBoundary.tsx)** – React Error Boundary  

### Seitenspezifische Components (`src/components/pages/`)
- **[`home/PartnerLogos.tsx`](mdc:src/components/pages/home/<USER>

### Import-Beispiele
```typescript
// Cookie Components
import { Banner, Button, ConsentWrapper } from "@/components/cookies";

// UI Components  
import { GlowCard, Toast } from "@/components/ui";

// Layout Components
import { Navbar, Footer } from "@/components/layout";

// Context
import { useCookieConsent, useToast } from "@/components/context";

// Seitenspezifische Components
import { PartnerLogos } from "@/components/pages/home";
```  

### Komponentenrichtlinien

- Komponenten sollen **klein & fokussiert** sein (<150 LOC).  
- Props und State klar typisieren.  
- Keine Inline-Styles für Layout – stattdessen CSS-Module (siehe `src/styles/`).  
- Animationslogik von Layout trennen.  

## Context-Provider

| Context | Datei | Verantwortlichkeit |
|---------|-------|--------------------|
| `CookieContext` | [`src/components/context/CookieContext.tsx`](mdc:src/components/context/CookieContext.tsx) | Verwaltung der Nutzer-Einwilligungen |
| `ToastContext` | [`src/components/context/ToastContext.tsx`](mdc:src/components/context/ToastContext.tsx) | Globale Toast-Benachrichtigungen |

Provider werden in [`src/main.tsx`](mdc:src/main.tsx) um `<App/>` herum eingebunden.

## Animations-Patterns

Framer Motion wird für Seitenübergänge und Mikro-Interaktionen verwendet.

```typescript
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  exit={{ opacity: 0, y: -20 }}
  transition={{ duration: 0.4, ease: "easeOut" }}
>
  {/* Inhalt */}
</motion.div>
```

**Best-Practices**:  
- Deduplizieren von `transition`-Props über Hilfsvariablen.  
- Vermeiden teurer box-shadow Animationen; stattdessen transform/opacity.  

## Styling-Guidelines

- Verwende CSS-Variablen (`:root`) für Farben & Spacing.  
- Mobile-First: erst kleine, dann größere Breakpoints.  
- Nutzen von `rem` statt `px` für skalierbare Typografie.  
- Fokus- & Hover-States barrierefrei gestalten.  

## Erweiterung von Komponenten

1. Neue Komponente unter `src/components/` anlegen.  
2. Falls globaler Zustand nötig ist, neuen Context unter `src/context/` erstellen.  
3. Dokumentation in diesem Markdown-Dokument ergänzen.  

---
Letzte Aktualisierung: 29.05.2025