---
description: Kontaktformular (Frontend + Backend)
globs: 
alwaysApply: false
---
# Kontaktformular – End-to-End-Dokumentation

Das Kontaktformular verbindet React-Frontend und Convex-Backend, um Nachrichten sicher zu speichern und per E-Mail zu versenden.

## Überblick

| Schicht | Hauptdateien | Zweck |
|---------|--------------|-------|
| Frontend | [`src/pages/kontakt/index.tsx`](mdc:src/pages/kontakt/index.tsx) | Formular UI, Validierung, Mutation-Aufruf |
| Backend DB | [`convex/kontaktformular/database.ts`](mdc:convex/kontaktformular/database.ts) | Mutation `send` – speichert Anfrage, triggert Mail |
| Backend Mail | [`convex/kontaktformular/email.ts`](mdc:convex/kontaktformular/email.ts) | Aktion `send` – SMTP-Mailversand |
| Schema | [`convex/schema.ts`](mdc:convex/schema.ts) | Tabelle `kontaktAnfragen` |

## Frontend-Implementation

### State & Validierung

```typescript
const [formData, setFormData] = useState({
  name: '', email: '', betreff: '', nachricht: '', zustimmungGegeben: false
});

const handleChange = (e: React.ChangeEvent<HTMLInputElement|HTMLTextAreaElement>) => {
  const { name, type } = e.target;
  const value = type === 'checkbox' ? (e.target as HTMLInputElement).checked : e.target.value;
  setFormData(prev => ({ ...prev, [name]: value }));
};
```

- **Pflichtfelder**: `name`, `email`, `nachricht`, `zustimmungGegeben`  
- E-Mail-Format via Regex prüfen.

### Submission Flow

```typescript
const kontaktSenden = useMutation(api.kontaktformular.database.send);

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  if (!formData.zustimmungGegeben) {
    showToast("Bitte Datenschutzerklärung akzeptieren.", "warning");
    return;
  }
  try {
    await kontaktSenden({ ...formData, zustimmung: formData.zustimmungGegeben });
    showToast("Nachricht erfolgreich gesendet!", "success");
    resetForm();
  } catch (err) {
    console.error(err);
    showToast("Fehler beim Senden.", "error");
  }
};
```

### DSGVO-Hinweis

- Checkbox **muss** aktiv bestätigt werden (keine Voreinstellung).  
- Hinweistext mit Link zur Datenschutzerklärung.

### Google Maps Einbettung

Externe Karte wird per [`ConsentWrapper`](mdc:src/components/ConsentWrapper.tsx) geschützt (Typ `external`).

## Backend-Implementation (Convex)

### Datenbank-Schema

```typescript
kontaktAnfragen: defineTable({
  name: v.string(),
  email: v.string(),
  betreff: v.string(),
  nachricht: v.string(),
  zustimmung: v.boolean(),
}).index("nach_email", ["email"]),
```

### Mutation `send`

1. **Insert** in `kontaktAnfragen`  
2. **Scheduler** startet Aktion `email.send` (0 Sekunden Delay)  
3. Gibt die neue ID zurück.

### Aktion `email.send`

```typescript
let transporter = nodemailer.createTransport({ host, port, secure, auth });
await transporter.sendMail({
  from: `"Kontaktformular" <${emailFrom}>`,
  to: emailTo,
  replyTo: args.email,
  subject: args.subject,
  html: emailTemplate(args),
});
```

- Lädt SMTP-Konfiguration aus **Convex Env** (siehe unten).  
- HTML-Template enthält alle Felder, IP & Zeitstempel.

## Umgebungsvariablen

| Key | Beschreibung |
|-----|--------------|
| `EMAIL_HOST` | SMTP-Server |
| `EMAIL_PORT` | Port (465/587) |
| `EMAIL_USER` | Benutzername |
| `EMAIL_PASSWORD` | Passwort |
| `EMAIL_FROM` | Absenderadresse |
| `EMAIL_TO` | Empfängeradresse |
| `EMAIL_SECURE` | `"true"` für SSL, sonst `"false"` |

> Tipp: Variablen in Convex Dashboard hinterlegen, **nicht** committen.

## Sicherheit & Spam-Schutz

- serverseitige Validierung in Mutation (Länge + Regex).  
- Option: reCAPTCHA-v3 Token als zusätzliches Argument übergeben und im Backend verifizieren.

## Test-Checkliste

- [ ] Erfolgreiche Speicherung + Mail ✅  
- [ ] Fehlermeldung bei ungültiger E-Mail ✅  
- [ ] DSGVO-Checkbox erzwungen ✅  
- [ ] Google Maps nur nach Consent sichtbar ✅  
- [ ] SMTP-Fehler werden abgefangen & geloggt ✅  

---
Letzte Aktualisierung: 29.05.2025