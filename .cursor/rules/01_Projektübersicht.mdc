---
description: Proje<PERSON><PERSON>bersicht
globs: 
alwaysApply: true
---
# Projektübersicht

Dieses Dokument liefert einen umfassenden Überblick über die innov8-IT Website, ihre technische Basis sowie die wichtigsten Strukturen und Konzepte.

## Tech-Stack

- **React 19** – UI-Bibliothek  
- **TypeScript** – Typensicherheit  
- **Vite** – Build- & Dev-Server  
- **Convex** – Serverless-Backend für Daten & E-Mails  
- **Framer Motion** – Animationen  
- **React Router** – Seiten-Routing  
- **BiomeJS** – Linting, Formatting & Code-Qualität  

## Verzeichnisstruktur (Top-Level)

```text
src/                # Frontend-Quellcode
├─ assets/          # Statische Assets
├─ components/      # Alle UI-Komponenten (organisiert nach Funktion)
│  ├─ cookies/      # Cookie-Management Components
│  ├─ ui/           # UI Components (Cards, Toasts, etc.)
│  ├─ layout/       # Layout Components (Navbar, Footer, etc.)
│  ├─ common/       # Gemeinsame Components (ErrorBoundary, etc.)
│  ├─ context/      # React-Context Provider
│  └─ pages/        # Seitenspezifische Components
│     └─ home/      # Home-spezifische Components
├─ pages/           # Seitenkomponenten (nur index.tsx pro Seite)
└─ styles/          # Globale CSS-Styles

public/             # Öffentliche Assets
convex/             # Backend-Funktionen (Convex)
```

## Import-System

Das Projekt verwendet moderne `@/` Alias-Imports für saubere und konsistente Importe:

```typescript
// Beispiel-Imports:
import { Banner, ConsentWrapper } from "@/components/cookies";
import { GlowCard, Toast } from "@/components/ui";
import { Navbar, Footer } from "@/components/layout";
import { useCookieConsent } from "@/components/context";
import Home from "@/pages/home";
```

## Kernbereiche

| Bereich            | Beschreibung                                   | Hauptdateien |
|--------------------|-------------------------------------------------|--------------|
| Frontend-Pages     | Landing- & Inhaltsseiten                        | [`src/pages/home/<USER>/pages/home/<USER>/pages/leistungen/index.tsx`](mdc:src/pages/leistungen/index.tsx) |
| UI-Komponenten     | Navbar, Footer, Karten, Animationen             | [`src/components/layout/Navbar.tsx`](mdc:src/components/layout/Navbar.tsx), [`src/components/ui/GlowCard.tsx`](mdc:src/components/ui/GlowCard.tsx) |
| Zustand / Kontext  | Cookie-Consent, Toasts                          | [`src/components/context/CookieContext.tsx`](mdc:src/components/context/CookieContext.tsx), [`src/components/context/ToastContext.tsx`](mdc:src/components/context/ToastContext.tsx) |
| Formular & Backend | Kontaktformular, E-Mail-Versand                 | [`src/pages/kontakt/index.tsx`](mdc:src/pages/kontakt/index.tsx), [`convex/kontaktformular/email.ts`](mdc:convex/kontaktformular/email.ts) |

## Wichtige Features

1. **Cookie-Consent-System** – DSGVO-konforme Steuerung externer Inhalte  
2. **Toast-Benachrichtigungen** – Einheitliches Feedback an Benutzer  
3. **Kontaktformular** – Validierung & serverseitiger Versand via Convex  
4. **Responsive Design** – Mobile-First, CSS-Variablen & Breakpoints  
5. **Animationsbibliothek** – Sanfte Seiten- & Elementübergänge  

## Design- & Style-Konzept

- Zentrales Farb- & Spacing-System über CSS-Variablen  
- Komponentenbasierte Styles für leichte Wartbarkeit  
- **Breakpoints**: 480 px (Mobile), 768 px (Tablet), 992 px (Desktop)  
- Fokus auf Barrierefreiheit (Kontrast, Tastaturnavigation, ARIA)  

## Performance-Strategien

- **Lazy Loading** von Bildern & Code-Splitting per React Router  
- GPU-beschleunigte Animationen  
- Responsive Images für unterschiedliche Geräte  

## Erweiterung

Neue Seiten können in drei Schritten hinzugefügt werden:

1. Komponente in `src/pages/[seitenname]/index.tsx` anlegen  
2. Route in [`src/App.tsx`](mdc:src/App.tsx) ergänzen  
3. Link in [`src/components/layout/Navbar.tsx`](mdc:src/components/layout/Navbar.tsx) aktualisieren  

Seitenspezifische Components gehören in `src/components/pages/[seitenname]/`.  

## Kontakt

Fragen zur Architektur oder Beitrag?  
✉ <EMAIL> | ☎ +49 2161 9850066