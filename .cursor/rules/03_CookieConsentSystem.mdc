---
description: <PERSON><PERSON><PERSON><PERSON><PERSON>-System
globs: 
alwaysApply: false
---
# <PERSON><PERSON>-<PERSON>sent-System

Das Cookie-Consent-System sorgt für DSGVO-konforme Einwilligungen und steuert das Nachladen externer Inhalte.

## Architektur­überblick

| Ebene | Datei | Aufgabe |
|-------|-------|---------|
| Context API | [`src/components/context/CookieContext.tsx`](mdc:src/components/context/CookieContext.tsx) | Speichert Präferenzen & stellt Helper-Funktionen |
| Banner | [`src/components/cookies/Banner.tsx`](mdc:src/components/cookies/Banner.tsx) | UI zum Akzeptieren / Ablehnen |
| Button | [`src/components/cookies/Button.tsx`](mdc:src/components/cookies/Button.tsx) | Schwebe-Button zum Öffnen des Banners |
| Wrapper | [`src/components/cookies/ConsentWrapper.tsx`](mdc:src/components/cookies/ConsentWrapper.tsx) | Blendet externe Inhalte bis zur Zustimmung aus |

## Kontext-Typen

```typescript
export type ConsentType = 'necessary' | 'external';

interface CookieConsent {
  necessary: boolean; // immer true
  external: boolean;  // Zustimmung für z. B. Google Maps
}

interface CookieContextType {
  consent: CookieConsent;
  showBanner: boolean;
  setShowBanner(show: boolean): void;
  acceptAll(): void;
  declineAll(): void;
  savePreferences(prefs: Partial<CookieConsent>): void;
  isConsentGiven(type: ConsentType): boolean;
}
```

### Hook

```typescript
export const useCookieConsent = () => {
  const ctx = React.useContext(CookieContext);
  if (!ctx) throw new Error('useCookieConsent must be used within a CookieProvider');
  return ctx;
};
```

## Lokale Speicherung

| Key | Inhalt | Ablaufzeit |
|-----|--------|------------|
| `sitePreferences` | JSON-String der `CookieConsent`-Daten | 3 Tage (konfigurierbar) |

```typescript
const COOKIE_EXPIRATION_DAYS = 3;

const saveConsentToCookies = (consent: CookieConsent) => {
  const expiry = new Date();
  expiry.setDate(expiry.getDate() + COOKIE_EXPIRATION_DAYS);
  localStorage.setItem('sitePreferences', JSON.stringify(consent));
};
```

## Komponenten-Details

### 1. Banner (ehemals CookieBanner)

Aufgaben:  
1. Informationstext darstellen  
2. Buttons *Alle akzeptieren*, *Alle ablehnen*, *Speichern*  
3. Individual­präferenzen (Toggle für `external`)  

> Tipp: Banner erst nach `hydrate` anzeigen, um Flash zu vermeiden.

### 2. Button (ehemals CookieButton)

Schwebender Button unten links, immer sichtbar.  
Öffnet den Banner via `setShowBanner(true)`.

### 3. ConsentWrapper

```typescript
<ConsentWrapper type="external" fallback={<Fallback />}>
  <iframe src="…google.com/maps…" />
</ConsentWrapper>
```

- Prüft `isConsentGiven(type)`  
- Zeigt `children` **nur** bei Zustimmung  
- Fallback sollte eine klare Handlungs­aufforderung enthalten

## Import-Beispiele

```typescript
// Cookie-System importieren
import { Banner, Button, ConsentWrapper } from "@/components/cookies";
import { useCookieConsent } from "@/components/context";

// Verwendung
const { isConsentGiven, setShowBanner } = useCookieConsent();
```

## Styling-Empfehlungen

```css
.cookie-banner {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  box-shadow: 0 8px 24px rgba(var(--text-color),.2);
  max-width: 480px;
  width: 90%;
  z-index: 1100;
}
.cookie-consent-btn { cursor: pointer; }
```

## Erweiterung

- Weitere Kategorien (z. B. *Analytics*) können durch Anpassen von `ConsentType` & `CookieConsent` ergänzt werden.  
- Beim Hinzufügen externer Dienste (z. B. YouTube) stets den `ConsentWrapper` verwenden.

## Testing-Checkliste

- [ ] Erstaufruf: Banner erscheint ✅  
- [ ] Präferenzen werden in `localStorage` gespeichert ✅  
- [ ] Externe Inhalte werden blockiert bis Zustimmung ✅  
- [ ] Banner lässt sich jederzeit über Button öffnen ✅  
- [ ] Ablauf der 3 Tage löscht Preferences ✅  

---
Letzte Aktualisierung: 29.05.2025