/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: '#3498db',
        secondary: '#2ecc71',
        accent: '#f39c12',
        'text-color': '#f5f5f5',
        background: '#121212',
        'card-background': '#1e1e1e',
        'header-background': '#0a0a0a',
        'footer-background': '#0a0a0a',
        'border-color': '#333',
        error: '#e74c3c',
      },
      fontFamily: {
        sans: [
          'Inter',
          'Segoe UI',
          'Roboto',
          'Oxygen',
          'Ubuntu',
          'Cantarell',
          'sans-serif',
        ],
      },
      animation: {
        'pulse-glow': 'pulse 3s infinite',
        'glitch-bg': 'glitch-bg 0.8s linear infinite',
      },
      keyframes: {
        pulse: {
          '0%, 100%': { opacity: '0' },
          '50%': { opacity: '0.8' },
        },
        'glitch-bg': {
          '0%': { backgroundPosition: '100% 0' },
          '100%': { backgroundPosition: '-100% 0' },
        },
      },
    },
  },
  plugins: [require('@tailwindcss/typography')],
};
